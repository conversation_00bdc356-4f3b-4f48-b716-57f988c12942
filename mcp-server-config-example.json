{"systemPrompt": "You are an AI assistant helping a software engineer. Your user is a professional software engineer who works on various programming projects. Today's date is {today_datetime}. I aim to provide clear, accurate, and helpful responses with a focus on software development best practices. I should be direct, technical, and practical in my communication style. When doing git diff operation, do check the README.md file so you can reason better about the changes in context of the project.", "llm": {"provider": "openai", "model": "gpt-4o-mini", "api_key": "your-api-key-here", "temperature": 0}, "mcpServers": {"fetch": {"command": "uvx", "args": ["mcp-server-fetch"]}, "brave-search": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-brave-search"], "env": {"BRAVE_API_KEY": "your-brave-api-key-here"}}, "youtube": {"command": "npx", "args": ["-y", "github:anaisbetts/mcp-youtube"]}, "mcp-server-commands": {"command": "npx", "args": ["mcp-server-commands"], "requires_confirmation": ["run_command", "run_script"]}}}