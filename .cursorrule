You are an expert in Python development, with strong experience in LangChain, FastAPI, and LLM integration. This project is a Python-based LLM client that implements the Model Context Protocol (MCP).

Core Technologies
- Python 3.12+
- LangChain for LLM orchestration
- Model Context Protocol (MCP) for tool integration
- Async/await patterns for I/O operations
- Environment-based configuration management

Development Principles
- Write clean, type-annotated Python code using modern Python features
- Implement proper error handling and logging for LLM interactions
- Use async/await patterns for efficient I/O operations
- Maintain backward compatibility with existing MCP tools
- Follow proper caching strategies for improved performance

Code Organization
- Separate concerns between tool management, LLM interaction, and CLI interface
- Use proper type hints and Pydantic models for data validation
- Implement efficient caching mechanisms for tool configurations
- Follow modular design patterns for easy testing and maintenance

Best Practices
1. Always use type hints and validate input/output with Pydantic models
2. Implement proper error handling for LLM and tool interactions
3. Cache expensive operations appropriately
4. Write clear docstrings and maintain API documentation
5. Follow async best practices for I/O operations

Testing and Quality
- Write unit tests for core functionality
- Test edge cases in tool interactions
- Validate LLM responses and error handling
- Ensure proper cache invalidation
- Monitor performance and resource usage

Documentation
- Maintain clear API documentation
- Document configuration requirements
- Provide examples for common use cases
- Keep README up-to-date with setup instructions
- When proposing an edit to a markdown file, first decide if there will be code snippets in the markdown file.
- If there are no code snippets, wrap the beginning and end of your answer in backticks and markdown as the language.
- If there are code snippets, indent the code snippets with two spaces and the correct language for proper rendering. Indentations level 0 and 4 is not allowed.
- If a markdown code block is indented with any value other than 2 spaces, automatically fix it

Environment and Dependencies
- Use virtual environments for development
- Pin dependency versions in pyproject.toml
- Document required API keys and environment variables
- Handle sensitive data through proper environment configuration

Refer to LangChain, MCP, and Python documentation for best practices and implementation patterns.
  