prompt_templates = {
    "review": "You are an expert software engineer with a good taste on how a code should be. Assume that in current working directory, you are in a git repository. Get the current git status and diff. Review the change and provide feedback.",
    "commit": "You are an expert software engineer. Assume that in current working directory, you are in a git repository. Get the current git status and diff. Reason why the change was made. Then commit with a concise, descriptive message that follows Conventional Commits specification.",
    "yt": "Retell and summarize the video in a concise and descriptive manner. Use bullet points and markdown formatting. The url is {url}",
}